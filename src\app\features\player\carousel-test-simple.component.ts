// carousel-test-simple.component.ts
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CarouselPlayerComponent, CarouselState, TransitionType } from './components/carousel-player.component';
import { Playlist, PlaylistItem } from '../../core/models/playlist.model';

@Component({
  selector: 'app-carousel-test-simple',
  standalone: true,
  imports: [CommonModule, CarouselPlayerComponent],
  template: `
    <div class="test-container">
      <h2>Simple Carousel Test - Fix Verification</h2>
      <p>This test verifies that the carousel slides to the next item and stays there without jumping back to the first item.</p>
      
      <div class="controls">
        <button (click)="startTest()" [disabled]="isPlaying">Start Auto-Play Test</button>
        <button (click)="pauseTest()" [disabled]="!isPlaying">Pause</button>
        <button (click)="resumeTest()" [disabled]="!isPaused">Resume</button>
        <button (click)="nextItem()">Manual Next →</button>
        <button (click)="prevItem()">← Manual Previous</button>
        <button (click)="setSlideTransition()">Use Slide Transition</button>
      </div>

      <div class="status" *ngIf="carouselState">
        <h3>Current Status:</h3>
        <p><strong>Status:</strong> {{ isPlaying ? 'Playing' : (isPaused ? 'Paused' : 'Stopped') }}</p>
        <p><strong>Current Item:</strong> {{ carouselState.currentIndex + 1 }} / {{ carouselState.totalItems }}</p>
        <p><strong>Item Name:</strong> {{ carouselState.currentItem?.name || 'None' }}</p>
        <p><strong>Progress:</strong> {{ carouselState.progress.toFixed(1) }}%</p>
        <p><strong>Time Remaining:</strong> {{ carouselState.timeRemaining.toFixed(1) }}s</p>
        <p><strong>Transition Type:</strong> {{ transitionType }}</p>
      </div>

      <div class="test-instructions">
        <h3>Test Instructions:</h3>
        <ol>
          <li>Click "Start Auto-Play Test" to begin automatic progression</li>
          <li>Watch the carousel slide from item 1 to item 2</li>
          <li>Verify that it stays on item 2 and doesn't jump back to item 1</li>
          <li>Let it continue to item 3, then item 4, then back to item 1 (loop)</li>
          <li>Use "Manual Next" to test manual transitions</li>
        </ol>
        <p><strong>Expected Behavior:</strong> Each slide transition should complete and stay on the target item.</p>
        <p><strong>Bug (if present):</strong> After sliding to next item, it immediately jumps back to first item.</p>
      </div>

      <div class="carousel-container">
        <app-carousel-player
          *ngIf="testPlaylist"
          #carouselPlayer
          [playlist]="testPlaylist"
          [autoPlay]="false"
          [loop]="true"
          [transitionType]="transitionType"
          [transitionDuration]="800"
          [showProgress]="true"
          (stateChange)="onStateChange($event)"
          (playlistEnded)="onPlaylistEnded()"
          (error)="onError($event)">
        </app-carousel-player>
      </div>

      <div class="error" *ngIf="errorMessage">
        <p><strong>Error:</strong> {{ errorMessage }}</p>
      </div>
    </div>
  `,
  styles: [`
    .test-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
      font-family: Arial, sans-serif;
    }

    .controls {
      margin: 20px 0;
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .controls button {
      padding: 10px 20px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
    }

    .controls button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }

    .controls button:hover:not(:disabled) {
      background: #0056b3;
    }

    .status {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
      border-left: 4px solid #007bff;
    }

    .status p {
      margin: 5px 0;
    }

    .test-instructions {
      background: #e7f3ff;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
      border-left: 4px solid #007bff;
    }

    .test-instructions ol {
      margin: 10px 0;
      padding-left: 20px;
    }

    .test-instructions li {
      margin: 5px 0;
    }

    .carousel-container {
      width: 100%;
      height: 400px;
      border: 2px solid #ddd;
      border-radius: 8px;
      overflow: hidden;
      margin: 20px 0;
      background: #000;
    }

    .error {
      background: #f8d7da;
      color: #721c24;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
      border-left: 4px solid #dc3545;
    }

    h2 {
      color: #333;
      margin-bottom: 10px;
    }

    h3 {
      color: #555;
      margin-bottom: 10px;
    }
  `]
})
export class CarouselTestSimpleComponent implements OnInit {
  @ViewChild('carouselPlayer') carouselPlayer!: CarouselPlayerComponent;

  testPlaylist: Playlist | null = null;
  carouselState: CarouselState | null = null;
  errorMessage: string | null = null;
  isPlaying = false;
  isPaused = false;
  transitionType: TransitionType = 'slide';

  ngOnInit(): void {
    this.createTestPlaylist();
  }

  private createTestPlaylist(): void {
    // Create a simple test playlist with short durations for quick testing
    const testItems: PlaylistItem[] = [
      {
        id: '1',
        type: 'image',
        name: 'Test Image 1 (Red)',
        duration: 3, // Short duration for quick testing
        content: {
          url: 'https://via.placeholder.com/1920x1080/ff0000/ffffff?text=Item+1+-+Red'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 800,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '2',
        type: 'image',
        name: 'Test Image 2 (Blue)',
        duration: 3,
        content: {
          url: 'https://via.placeholder.com/1920x1080/0000ff/ffffff?text=Item+2+-+Blue'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 800,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '3',
        type: 'image',
        name: 'Test Image 3 (Green)',
        duration: 3,
        content: {
          url: 'https://via.placeholder.com/1920x1080/00ff00/ffffff?text=Item+3+-+Green'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 800,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '4',
        type: 'image',
        name: 'Test Image 4 (Yellow)',
        duration: 3,
        content: {
          url: 'https://via.placeholder.com/1920x1080/ffff00/000000?text=Item+4+-+Yellow'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 800,
          scaling: 'stretch'
        },
        schedule: null
      }
    ];

    this.testPlaylist = {
      id: 'simple-test-playlist',
      name: 'Simple Carousel Test Playlist',
      description: 'A simple test playlist to verify carousel fix',
      duration: testItems.reduce((total, item) => total + item.duration, 0),
      items: testItems,
      lastModified: new Date().toISOString(),
      createdBy: 'test-user',
      status: 'active',
      tags: ['test', 'simple'],
      settings: {
        autoPlay: true,
        loop: true,
        defaultMuted: true,
        transition: {
          type: 'slide',
          duration: 0.8
        },
        defaultDuration: 3,
        scheduling: {
          enabled: false,
          priority: 1
        }
      }
    };
  }

  startTest(): void {
    if (this.testPlaylist && this.carouselPlayer) {
      this.carouselPlayer.startPlayback();
      this.isPlaying = true;
      this.isPaused = false;
      console.log('Starting simple carousel test');
    }
  }

  pauseTest(): void {
    if (this.carouselPlayer) {
      this.carouselPlayer.pause();
    }
    console.log('Pausing carousel test');
  }

  resumeTest(): void {
    if (this.carouselPlayer) {
      this.carouselPlayer.resume();
    }
    console.log('Resuming carousel test');
  }

  nextItem(): void {
    if (this.carouselPlayer) {
      this.carouselPlayer.next();
    }
    console.log('Manual next item requested');
  }

  prevItem(): void {
    if (this.carouselPlayer) {
      this.carouselPlayer.previous();
    }
    console.log('Manual previous item requested');
  }

  setSlideTransition(): void {
    this.transitionType = 'slide';
    console.log('Transition type set to slide');
  }

  onStateChange(state: CarouselState): void {
    this.carouselState = state;
    this.isPlaying = state.isPlaying;
    this.isPaused = state.isPaused;
    console.log('State changed:', {
      currentIndex: state.currentIndex,
      currentItem: state.currentItem?.name,
      isPlaying: state.isPlaying,
      isPaused: state.isPaused
    });
  }

  onPlaylistEnded(): void {
    console.log('Playlist ended');
    this.isPlaying = false;
    this.isPaused = false;
  }

  onError(error: string): void {
    this.errorMessage = error;
    console.error('Carousel error:', error);
  }
}
