// app-routing.module.ts
import { Routes } from '@angular/router';
import { AuthGuard } from './core/guard/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'player',
    pathMatch: 'full'
  },
  {
    path: 'registration',
    loadComponent: () => import('./features/registration/registration.component').then(m => m.RegistrationComponent),
    // Disable SSR for this route
    data: { ssr: false }
  },
  {
    path: 'player',
    loadComponent: () => import('./features/player/player.component').then(m => m.PlayerComponent),
   canActivate: [AuthGuard],
    // Disable SSR for this route
    data: { ssr: false }
  },
  {
    path: 'diagnostics',
    loadComponent: () => import('./features/diagnostics/diagnostics.component').then(m => m.DiagnosticsComponent),
    canActivate: [AuthGuard],
    // Disable SSR for this route
    data: { ssr: false }
  },
  {
    path: 'test-carousel',
    loadComponent: () => import('./features/player/test-carousel.component').then(m => m.TestCarouselComponent),
    // Disable SSR for this route
    data: { ssr: false }
  },
  {
    path: 'carousel-demo',
    loadComponent: () => import('./features/player/carousel-demo.component').then(m => m.CarouselDemoComponent),
    // Disable SSR for this route
    data: { ssr: false }
  },
  {
    path: 'carousel-test-simple',
    loadComponent: () => import('./features/player/carousel-test-simple.component').then(m => m.CarouselTestSimpleComponent),
    // Disable SSR for this route
    data: { ssr: false }
  },
  {
    path: '**',
    redirectTo: 'player'
  }
];