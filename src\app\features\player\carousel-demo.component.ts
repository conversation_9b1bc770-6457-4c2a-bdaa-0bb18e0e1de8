// carousel-demo.component.ts
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CarouselPlayerComponent, CarouselState, TransitionType } from './components/carousel-player.component';
import { Playlist, PlaylistItem } from '../../core/models/playlist.model';

@Component({
  selector: 'app-carousel-demo',
  standalone: true,
  imports: [CommonModule, CarouselPlayerComponent],
  template: `
    <div class="demo-container">
      <div class="demo-header">
        <h1>🎠 Enhanced Carousel Player Demo</h1>
        <p>Experience the new carousel-based media player with smooth slide transitions</p>
      </div>

      <div class="demo-controls">
        <div class="control-group">
          <h3>Playback Controls</h3>
          <button class="btn primary" (click)="startDemo()" [disabled]="isPlaying">
            ▶️ Start Demo
          </button>
          <button class="btn secondary" (click)="pauseDemo()" [disabled]="!isPlaying || isPaused">
            ⏸️ Pause
          </button>
          <button class="btn secondary" (click)="resumeDemo()" [disabled]="!isPaused">
            ▶️ Resume
          </button>
          <button class="btn danger" (click)="stopDemo()">
            ⏹️ Stop
          </button>
        </div>

        <div class="control-group">
          <h3>Navigation</h3>
          <button class="btn nav" (click)="previousSlide()">
            ⬅️ Previous
          </button>
          <button class="btn nav" (click)="nextSlide()">
            ➡️ Next
          </button>
          <button class="btn secondary" (click)="goToSlide(0)">
            🏠 First
          </button>
          <button class="btn secondary" (click)="goToSlide((demoPlaylist?.items?.length || 1) - 1)">
            🏁 Last
          </button>
        </div>

        <div class="control-group">
          <h3>Transition Effects</h3>
          <button 
            class="btn" 
            [class.active]="transitionType === 'slide'"
            (click)="setTransition('slide')">
            🎠 Slide
          </button>
          <button 
            class="btn" 
            [class.active]="transitionType === 'fade'"
            (click)="setTransition('fade')">
            ✨ Fade
          </button>
          <button 
            class="btn" 
            [class.active]="transitionType === 'none'"
            (click)="setTransition('none')">
            ⚡ Instant
          </button>
        </div>
      </div>

      <div class="demo-status" *ngIf="carouselState">
        <div class="status-card">
          <h3>📊 Status</h3>
          <div class="status-grid">
            <div class="status-item">
              <span class="label">State:</span>
              <span class="value" [class]="getStatusClass()">
                {{ getStatusText() }}
              </span>
            </div>
            <div class="status-item">
              <span class="label">Slide:</span>
              <span class="value">{{ carouselState.currentIndex + 1 }} / {{ carouselState.totalItems }}</span>
            </div>
            <div class="status-item">
              <span class="label">Progress:</span>
              <span class="value">{{ carouselState.progress.toFixed(1) }}%</span>
            </div>
            <div class="status-item">
              <span class="label">Time Left:</span>
              <span class="value">{{ carouselState.timeRemaining.toFixed(1) }}s</span>
            </div>
            <div class="status-item">
              <span class="label">Current:</span>
              <span class="value">{{ carouselState.currentItem?.name || 'None' }}</span>
            </div>
            <div class="status-item">
              <span class="label">Transition:</span>
              <span class="value">{{ transitionType }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="demo-carousel">
        <app-carousel-player
          *ngIf="demoPlaylist"
          #carouselPlayer
          [playlist]="demoPlaylist"
          [autoPlay]="false"
          [loop]="true"
          [transitionType]="transitionType"
          [transitionDuration]="transitionDuration"
          [showProgress]="true"
          (stateChange)="onStateChange($event)"
          (playlistEnded)="onPlaylistEnded()"
          (error)="onError($event)">
        </app-carousel-player>
      </div>

      <div class="demo-error" *ngIf="errorMessage">
        <div class="error-card">
          <h3>❌ Error</h3>
          <p>{{ errorMessage }}</p>
          <button class="btn primary" (click)="clearError()">Clear</button>
        </div>
      </div>

      <div class="demo-info">
        <h3>🎯 Features Demonstrated</h3>
        <ul>
          <li>✅ <strong>Smooth slide transitions</strong> - Like a traditional carousel</li>
          <li>✅ <strong>Multiple transition types</strong> - Slide, fade, and instant</li>
          <li>✅ <strong>Automatic timed progression</strong> - Configurable durations</li>
          <li>✅ <strong>Manual navigation</strong> - Previous/next controls</li>
          <li>✅ <strong>Progress tracking</strong> - Real-time progress indicators</li>
          <li>✅ <strong>Pause/resume functionality</strong> - Full playback control</li>
          <li>✅ <strong>Error handling</strong> - Graceful error recovery</li>
          <li>✅ <strong>Media type support</strong> - Images, videos, and web content</li>
        </ul>
      </div>
    </div>
  `,
  styleUrls: ['./carousel-demo.component.scss']
})
export class CarouselDemoComponent implements OnInit {
  @ViewChild('carouselPlayer') carouselPlayer!: CarouselPlayerComponent;
  
  demoPlaylist: Playlist | null = null;
  carouselState: CarouselState | null = null;
  errorMessage: string | null = null;
  isPlaying = false;
  isPaused = false;
  transitionType: TransitionType = 'slide';
  transitionDuration = 600;

  ngOnInit(): void {
    this.createDemoPlaylist();
  }

  private createDemoPlaylist(): void {
    const demoItems: PlaylistItem[] = [
      {
        id: '1',
        type: 'image',
        name: 'Beautiful Landscape',
        duration: 4,
        content: {
          url: 'https://picsum.photos/1920/1080?random=1&blur=0'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 600,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '2',
        type: 'image',
        name: 'City Skyline',
        duration: 5,
        content: {
          url: 'https://picsum.photos/1920/1080?random=2&blur=0'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 600,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '3',
        type: 'image',
        name: 'Nature Scene',
        duration: 3,
        content: {
          url: 'https://picsum.photos/1920/1080?random=3&blur=0'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 600,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '4',
        type: 'webpage',
        name: 'Example Website',
        duration: 6,
        content: {
          url: 'https://example.com'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 600,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '5',
        type: 'image',
        name: 'Abstract Art',
        duration: 4,
        content: {
          url: 'https://picsum.photos/1920/1080?random=5&blur=0'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 600,
          scaling: 'stretch'
        },
        schedule: null
      }
    ];

    this.demoPlaylist = {
      id: 'demo-playlist',
      name: 'Carousel Demo Playlist',
      description: 'Demonstration of the enhanced carousel player',
      duration: demoItems.reduce((total, item) => total + item.duration, 0),
      items: demoItems,
      lastModified: new Date().toISOString(),
      createdBy: 'demo-user',
      status: 'active',
      tags: ['demo', 'carousel'],
      settings: {
        autoPlay: true,
        loop: true,
        defaultMuted: true,
        transition: {
          type: 'slide',
          duration: 0.6
        },
        defaultDuration: 4,
        scheduling: {
          enabled: false,
          priority: 1
        }
      }
    };
  }

  // Control methods
  startDemo(): void {
    if (this.carouselPlayer) {
      this.carouselPlayer.startPlayback();
    }
  }

  pauseDemo(): void {
    if (this.carouselPlayer) {
      this.carouselPlayer.pause();
    }
  }

  resumeDemo(): void {
    if (this.carouselPlayer) {
      this.carouselPlayer.resume();
    }
  }

  stopDemo(): void {
    // Stop functionality would be implemented here
    this.isPlaying = false;
    this.isPaused = false;
  }

  nextSlide(): void {
    if (this.carouselPlayer) {
      this.carouselPlayer.next();
    }
  }

  previousSlide(): void {
    if (this.carouselPlayer) {
      this.carouselPlayer.previous();
    }
  }

  goToSlide(index: number | undefined): void {
    if (this.carouselPlayer && typeof index === 'number' && index >= 0) {
      this.carouselPlayer.goToItem(index);
    }
  }

  setTransition(type: TransitionType): void {
    this.transitionType = type;
  }

  // Event handlers
  onStateChange(state: CarouselState): void {
    this.carouselState = state;
    this.isPlaying = state.isPlaying;
    this.isPaused = state.isPaused;
  }

  onPlaylistEnded(): void {
    console.log('Demo playlist ended');
  }

  onError(error: string): void {
    this.errorMessage = error;
  }

  clearError(): void {
    this.errorMessage = null;
  }

  // Helper methods
  getStatusText(): string {
    if (this.isPlaying && !this.isPaused) return 'Playing';
    if (this.isPaused) return 'Paused';
    return 'Stopped';
  }

  getStatusClass(): string {
    if (this.isPlaying && !this.isPaused) return 'playing';
    if (this.isPaused) return 'paused';
    return 'stopped';
  }
}
